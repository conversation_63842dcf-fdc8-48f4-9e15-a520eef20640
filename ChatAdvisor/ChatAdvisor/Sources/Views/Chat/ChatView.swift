import Foundation
import Localize_Swift
import SimpleToast
import SwiftUI

struct ChatView: View {
    @Binding var showSideMenu: Bool
    @ObservedObject var bootManager = BootManager.shared
    @EnvironmentObject var viewModel: ChatViewModel
    @EnvironmentObject var contentViewModel: ContentViewModel

    var body: some View {
        VStack {
            // 新增：网络状态指示器
            NetworkStatusIndicator()

            ZStack(alignment: .center) {
                // 聊天详情加载指示器和错误显示（只在初始加载时显示）
                if contentViewModel.isLoadingChatDetails || viewModel.isLoadingInitialMessages {
                    VStack {
                        Spacer()
                        VStack(spacing: 16) {
                            ProgressView()
                                .scaleEffect(1.2)
                                .tint(.accentColor)
                            Text("正在加载聊天记录...")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                        .padding(20)
                        .background(Color(.systemBackground).opacity(0.95))
                        .cornerRadius(16)
                        .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
                        Spacer()
                    }
                    .transition(.opacity.combined(with: .scale))
                    .zIndex(1)
                }

                // 错误状态显示
                if let errorMessage = contentViewModel.chatLoadingError {
                    VStack {
                        Spacer()
                        VStack(spacing: 16) {
                            Image(systemName: "exclamationmark.triangle.fill")
                                .font(.largeTitle)
                                .foregroundColor(.orange)
                            Text(errorMessage)
                                .font(.caption)
                                .foregroundColor(.secondary)
                                .multilineTextAlignment(.center)
                            Button("重试") {
                                contentViewModel.chatLoadingError = nil
                            }
                            .buttonStyle(.borderedProminent)
                            .tint(.accentColor)
                        }
                        .padding(20)
                        .background(Color(.systemBackground).opacity(0.95))
                        .cornerRadius(16)
                        .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
                        Spacer()
                    }
                    .transition(.opacity.combined(with: .scale))
                    .zIndex(1)
                }
                ScrollViewReader { proxy in
                    ScrollView {
                        LazyVStack(spacing: 0) {
                            // 空状态显示
                            if viewModel.currentChat.messages.filter({ $0.role != .system }).isEmpty && !viewModel.isLoadingInitialMessages {
                                VStack(spacing: 20) {
                                    Spacer()
                                    Image(systemName: "message.circle")
                                        .font(.system(size: 60))
                                        .foregroundColor(.secondary.opacity(0.6))

                                    VStack(spacing: 8) {
                                        Text("开始新的对话")
                                            .font(.title2)
                                            .fontWeight(.medium)
                                            .foregroundColor(.primary)

                                        Text("在下方输入框中输入消息开始聊天")
                                            .font(.body)
                                            .foregroundColor(.secondary)
                                            .multilineTextAlignment(.center)
                                    }
                                    Spacer()
                                }
                                .frame(maxWidth: .infinity)
                                .padding()
                            }

                            ForEach(viewModel.currentChat.messages.filter { $0.role != .system }, id: \.viewId) { message in
                                MessageBubble(message: message)
                                    .environmentObject(viewModel) // 传递ChatViewModel环境对象
                                    .contextMenu {
                                        Button(action: { UIPasteboard.general.string = message.content }) {
                                            Label("复制".localized(), systemImage: "doc.on.doc")
                                        }
                                        Button(action: { viewModel.readAloud(text: message.content) }) {
                                            Label("朗读".localized(), systemImage: "speaker.wave.2")
                                        }
                                        Button(action: { viewModel.selectedMessage = message }) {
                                            Label("选择文本".localized(), systemImage: "text.cursor")
                                        }
                                    }
                                    .padding(.horizontal, AppThemes.padding)
                                    .id(message.viewId)
                                    .onAppear {
                                        if message == viewModel.currentChat.messages.filter({ $0.role != .system }).first {
                                            viewModel.loadMoreIfNeeds()
                                        }
                                    }
                            }

                            // 新增：AI输入指示器
                            TypingIndicator(state: viewModel.aiTypingState)
                                .id("typing-indicator")

                            // 滚动位置检测器
                            ScrollPositionReader(
                                onPositionChange: { isNearBottom in
                                    viewModel.scrollManager.updateUserPosition(isNearBottom: isNearBottom)
                                },
                                onDetailedPositionChange: { position in
                                    viewModel.scrollManager.updateScrollPosition(position)

                                    // 智能预加载逻辑
                                    if viewModel.scrollManager.shouldTriggerPreload {
                                        viewModel.loadMoreIfNeeds()
                                    }
                                }
                            )

                            Color.clear
                                .frame(height: 24)
                                .id("bottom")
                        }
                        // 优化的滚动逻辑 - 使用防抖动机制
                        .onChange(of: viewModel.currentChat.messages.count) { newCount in
                            viewModel.handleMessageCountChange(newCount: newCount) {
                                if viewModel.scrollManager.shouldAutoScroll {
                                    withAnimation(.easeOut(duration: 0.3)) {
                                        proxy.scrollTo("bottom", anchor: .bottom)
                                    }
                                    viewModel.scrollManager.clearNewMessages()
                                }
                            }
                        }
                        .onChange(of: viewModel.currentChat.messages.last?.content) { newContent in
                            viewModel.handleLastMessageContentChange(newContent: newContent) {
                                if viewModel.scrollManager.shouldAutoScroll {
                                    withAnimation(.easeOut(duration: 0.2)) {
                                        proxy.scrollTo("bottom", anchor: .bottom)
                                    }
                                }
                            }
                        }
                    }
                    .coordinateSpace(name: "scroll")
                    .animation(nil, value: viewModel.currentChat.messages.count)
                    .sheet(item: $viewModel.selectedMessage) { message in
                        TextSelectionView(message: $viewModel.selectedMessage)
                    }
                }
            }


            HStack {
                InputBottomView()
                    .environmentObject(viewModel)
            }
            .padding(.horizontal, AppThemes.padding / 2)
        }
        .simpleToast(isPresented: $viewModel.showToast, options: AppThemes.toastOptions) {
            toastLabel
        }
        .background(Color(UIColor.systemBackground))
        .coordinateSpace(name: "frameLayer")
        .onAppear {
            handleOnAppear()
        }
        .onChange(of: contentViewModel.selectedChatID) { newChatID in
            // 当选中的会话ID改变时，确保消息内容正确加载
            if let newChatID = newChatID {
                // 如果当前ChatView显示的会话与选中的会话ID匹配，但消息为空，则加载消息
                if viewModel.currentChat.id == newChatID &&
                   viewModel.currentChat.messages.isEmpty &&
                   !viewModel.isLoadingInitialMessages &&
                   !viewModel.isLoadingMessage {
                    Task {
                        await viewModel.fetchCurrentChatMessages()
                    }
                }
                // 注意：ChatView的environmentObject会根据ContentView的逻辑自动更新
                // 所以会话ID不匹配是正常的过渡状态，不需要警告
            }
        }
    }

    private var toastLabel: some View {
        Label(viewModel.toastMessage, systemImage: "exclamationmark.triangle")
            .padding()
            .background(viewModel.isRequestError ? Color.red.opacity(0.8) : Color.mainDark)
            .foregroundColor(.white)
            .cornerRadius(10)
            .padding(.top, 120)
    }

    private var helpLabel: some View {
        Label("chat_help".localized(), systemImage: "questionmark.circle")
            .padding()
            .background(viewModel.isRequestError ? Color.red.opacity(0.8) : Color.mainDark)
            .foregroundColor(.white)
            .cornerRadius(10)
            .padding(.top, 120)
    }

    private func handleOnAppear() {
        if ChatViewModel.allModels.count == 0 {
            viewModel.getPricing()
        } else {
            viewModel.currentModel = ChatViewModel.allModels.first ?? ChatsModel.default
        }
        Task {
            await viewModel.loadOlderMessages()
        }
    }
    
    private func scrollToBottomSmoothly(with proxy: ScrollViewProxy) {
        withAnimation(.easeInOut(duration: 0.25)) {
            proxy.scrollTo("bottom", anchor: .bottom)
        }
    }
}
